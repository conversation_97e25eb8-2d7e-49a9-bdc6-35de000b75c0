from django.core.exceptions import ValidationError
from django.db import models
from django.db import transaction
from django.utils import timezone
from datetime import timedelta

from core.generate_hashid import generate_resource_uuid
from core.model import BaseModel
from file.models import StaffContractFile, StaffQualificationCertificateFile
from maternity_center.models import MaternityCenter
from message.utils import send_certificate_expiry_message
from user.models import Staff
from .enum import OrgScheduleStatusEnum, ShiftTypeEnum, TrainingResultEnum


# 月子中心排班表
class Schedule(BaseModel):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心", related_name='schedules')
    # 员工
    staff = models.ForeignKey(Staff, on_delete=models.CASCADE, verbose_name="员工", related_name='schedules')
    # 岗位
    position = models.CharField(max_length=20, verbose_name="岗位")
    # 排班日期
    schedule_date = models.DateField(verbose_name="排班日期")
    # 班次类型
    shift_type = models.CharField(verbose_name="班次类型", max_length=20, choices=ShiftTypeEnum.choices)
    # 备注
    remark = models.TextField(verbose_name="备注", blank=True, default='')
    # 状态
    status = models.CharField(verbose_name="状态", max_length=20, choices=OrgScheduleStatusEnum.choices, default=OrgScheduleStatusEnum.PENDING)
    # rid
    rid = models.CharField(max_length=100, verbose_name="rid", default=generate_resource_uuid)

    class Meta:
        verbose_name = "机构排班表"
        verbose_name_plural = "机构排班表"
        unique_together = ['staff', 'schedule_date', 'shift_type']

    def __str__(self):
        return f"{self.staff.name} - {self.schedule_date} - {self.get_shift_type_display()}"


# # 交接班记录表
# class HandoverRecord(BaseModel):
#     # 月子中心排班表
#     schedule = models.ForeignKey(Schedule, on_delete=models.CASCADE, verbose_name="月子中心排班表", related_name='handover_records')
#     # 交班员工
#     handover_staff = models.ForeignKey(Staff, on_delete=models.CASCADE, verbose_name="交班员工", related_name='as_handover_staff_records')
#     # 接班员工
#     next_staff = models.ForeignKey(Staff, on_delete=models.CASCADE, verbose_name="接班员工", related_name='as_next_staff_records')
#     # 交班时间
#     shift_time = models.DateTimeField(verbose_name="交班时间")
#     # 状态
#     status = models.CharField(verbose_name="状态", max_length=20, choices=HandoverStatusEnum.choices, default=HandoverStatusEnum.DRAFT)

#     class Meta:
#         verbose_name = "交接班记录表"
#         verbose_name_plural = "交接班记录表"



# 员工合同
class StaffContract(BaseModel):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心", blank=True, null=True)
    # 员工
    staff = models.ForeignKey(Staff, on_delete=models.CASCADE, verbose_name="员工", blank=True, null=True,related_name='staff_contract_files')
    # 合同编号
    contract_number = models.CharField(max_length=100, verbose_name="合同编号", blank=True, null=True,default='')
    # 合同有效期
    contract_validity_period = models.DateField(verbose_name="合同有效期", blank=True, null=True)
    # 文件
    file = models.ForeignKey(StaffContractFile, on_delete=models.CASCADE, verbose_name="文件", blank=True,null=True,related_name='staff_contracts')
    # rid
    rid = models.CharField(max_length=100, verbose_name="rid", default=generate_resource_uuid)
    # 创建人
    creator = models.ForeignKey(Staff, on_delete=models.SET_NULL, verbose_name="创建人", blank=True, null=True,related_name='creator_staff_contracts')
    
    class Meta:
        verbose_name = "员工合同"
        verbose_name_plural = "员工合同"
    
    def __str__(self):
        return f"{self.staff.name} - {self.contract_number}"
    

# 员工资质证书
class StaffQualificationCertificate(BaseModel):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心", blank=True, null=True)
    # 员工
    staff = models.ForeignKey(Staff, on_delete=models.CASCADE, verbose_name="员工", blank=True, null=True,related_name='staff_qualification_certificates')
    # 资质证书文件
    file = models.ForeignKey(StaffQualificationCertificateFile, on_delete=models.CASCADE, verbose_name="资质证书文件", blank=True, null=True)
    # 颁发机构
    issuing_authority = models.CharField(max_length=100, verbose_name="颁发机构", blank=True, null=True,default='')
    # 证书名称
    certificate_name = models.CharField(max_length=100, verbose_name="证书名称", blank=True, null=True,default='')
    # 有效期
    validity_period = models.DateField(verbose_name="有效期", blank=True, null=True)
    # 创建时间
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    # 创建人
    creator = models.ForeignKey(Staff, on_delete=models.CASCADE, verbose_name="创建人", blank=True, null=True,related_name='creator_staff_qualification_certificates')
    # rid
    rid = models.CharField(max_length=100, verbose_name="rid", default=generate_resource_uuid)
    
    class Meta:
        verbose_name = "员工资质证书"
        verbose_name_plural = "员工资质证书"
        
    @classmethod
    def check_certificate_expiry(cls):
        
        today = timezone.now().date()

        # 提醒时间点（到期前的天数）
        reminder_days = [30, 15, 7, 3, 1]

        # 获取所有有效期不为空的证书
        certificates = cls.objects.filter(validity_period__isnull=False)

        for certificate in certificates:
            # 计算距离到期的天数
            days_until_expiry = (certificate.validity_period - today).days

            # 检查是否需要发送提醒
            if days_until_expiry in reminder_days:

                send_certificate_expiry_message(certificate.staff.phone, {
                        "certificate_name":certificate.certificate_name,
                        "expiry_date":certificate.validity_period.strftime("%Y-%m-%d")
                    })



# 员工培训记录
class StaffTrainingRecord(BaseModel):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心", blank=True, null=True)
    # 培训主题	
    training_topics = models.CharField(max_length=100, verbose_name="培训主题", blank=True, null=True,default='')
    # 参训员工
    staff = models.ForeignKey(Staff, on_delete=models.CASCADE, verbose_name="参训员工", blank=True, null=True,related_name='staff_training_records')
    # 培训日期
    training_date = models.DateField(verbose_name="培训日期", blank=True, null=True)
    # 培训时长
    training_duration = models.IntegerField(verbose_name="培训时长", blank=True, null=True)
    # 结果	
    training_result = models.CharField(max_length=100, verbose_name="培训结果", blank=True, null=True,
                                       choices=TrainingResultEnum.choices,default=TrainingResultEnum.ABSENT)
    # 附件
    attachment = models.JSONField(verbose_name="附件", blank=True,default=list)
    # rid
    rid = models.CharField(max_length=100, verbose_name="rid", default=generate_resource_uuid)
    
    class Meta:
        verbose_name = "员工培训记录"
        verbose_name_plural = "员工培训记录"
        
    
        
        
# 员工考核记录
class StaffAssessmentRecord(BaseModel):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心", blank=True, null=True)
    # 被考核员工
    staff = models.ForeignKey(Staff, on_delete=models.CASCADE, verbose_name="被考核员工", blank=True, null=True,related_name='staff_assessment_records')
    # 考核名称
    assessment_name = models.CharField(max_length=100, verbose_name="考核名称", blank=True, null=True,default='')
    # 考核日期
    assessment_date = models.DateField(verbose_name="考核日期", blank=True, null=True)
    # 考核对象
    assessment_object = models.ForeignKey(Staff, on_delete=models.CASCADE, verbose_name="考核对象", blank=True, null=True)
    # 考核结果
    assessment_result = models.CharField(max_length=100, verbose_name="考核结果", blank=True, null=True,default='')
    # 考核评语
    assessment_comment = models.TextField(verbose_name="考核评语", blank=True, null=True,default='')
    # 附件
    attachment = models.JSONField(verbose_name="附件", blank=True,default=list)
    # rid
    rid = models.CharField(max_length=100, verbose_name="rid", default=generate_resource_uuid)
    
    class Meta:
        verbose_name = "员工考核记录"
        verbose_name_plural = "员工考核记录"
        
        

# 员工健康检查记录
class StaffHealthCheckRecord(BaseModel):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心", blank=True, null=True)
    # 员工
    staff = models.ForeignKey(Staff, on_delete=models.CASCADE, verbose_name="员工", blank=True, null=True,related_name='staff_health_check_records')
    # 健康检查项目
    health_check_project = models.CharField(max_length=100, verbose_name="健康检查项目", blank=True, null=True,default='')
    # 健康检查日期
    health_check_date = models.DateField(verbose_name="健康检查日期", blank=True, null=True)
    # 健康检查结果
    health_check_result = models.CharField(max_length=100, verbose_name="健康检查结果", blank=True, null=True,default='')
    # 附件
    attachment = models.JSONField(verbose_name="附件", blank=True,default=list)
    # rid
    rid = models.CharField(max_length=100, verbose_name="rid", default=generate_resource_uuid)
    
    class Meta:
        verbose_name = "员工健康检查记录"
        verbose_name_plural = "员工健康检查记录"