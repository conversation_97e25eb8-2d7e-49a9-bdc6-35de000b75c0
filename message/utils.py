from message.core import get_sms_client
from message.exceptions import SMSNetworkException


def send_audit_message(phone):

    try:
        client = get_sms_client()
        result = client.send_audit_sms(phone, {
            "application_name":"换房申请",
            "audit_result":"被拒绝",
            "audit_time":"2025-07-29 17:00"
        })
        print(result)
    except Exception as e:
        print(e)
        
        

# 发送证书过期提醒短信
def send_certificate_expiry_message(phone,template_params):
    try:
        client = get_sms_client()
        result = client.send_sms_with_template(
            'SMS_491445498',
            phone,
            template_params,
            )
        print(result)
    
    except Exception as e:
        print(e)
        pass
        
        

